import express from 'express';
import User from '../models/User.js';
import Course from '../models/Course.js';
import { verifyToken } from '../middleware/verifyToken.js';
import { allowRoles } from '../middleware/adminOnly.js';

const router = express.Router();

// Get basic user list (admin can see limited info, super-admin gets full access via super-admin routes)
router.get('/users', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    // Regular admins get limited user info, super-admins should use super-admin routes
    if (req.user.role === 'admin') {
      const users = await User.find()
        .select('name email role createdAt isActive')
        .sort({ createdAt: -1 });
      res.json(users);
    } else {
      // Redirect super-admins to use super-admin routes for full functionality
      res.status(200).json({
        message: 'Use /api/super-admin/users for full user management features',
        redirectTo: '/api/super-admin/users'
      });
    }
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Update user role - RESTRICTED: Only super-admin can change roles
router.put('/users/:id/role', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    if (req.user.role === 'admin') {
      return res.status(403).json({
        message: 'Access denied. Only super-admin can change user roles.',
        redirectTo: '/api/super-admin/users/:id/role'
      });
    }

    // If super-admin, redirect to proper endpoint
    res.status(200).json({
      message: 'Use /api/super-admin/users/:id/role for role management',
      redirectTo: '/api/super-admin/users/:id/role'
    });
  } catch (error) {
    console.error('Error in role update:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Delete user - RESTRICTED: Only super-admin via super-admin routes
router.delete('/users/:id', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    if (req.user.role === 'admin') {
      return res.status(403).json({
        message: 'Access denied. Only super-admin can delete users.',
        redirectTo: '/api/super-admin/users/:id'
      });
    }

    // If super-admin, redirect to proper endpoint
    res.status(200).json({
      message: 'Use /api/super-admin/users/:id for user deletion',
      redirectTo: '/api/super-admin/users/:id'
    });
  } catch (error) {
    console.error('Error in user deletion:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get all courses for admin (including inactive)
router.get('/courses', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    const courses = await Course.find().sort({ createdAt: -1 });
    res.json(courses);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get dashboard statistics (different data based on role)
router.get('/statistics', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    if (req.user.role === 'super-admin') {
      // Redirect super-admins to get detailed statistics
      return res.status(200).json({
        message: 'Use /api/super-admin/statistics for detailed system statistics',
        redirectTo: '/api/super-admin/statistics'
      });
    }

    // Basic statistics for regular admins (course-focused)
    const totalCourses = await Course.countDocuments();
    const activeCourses = await Course.countDocuments({ isActive: true });
    const inactiveCourses = await Course.countDocuments({ isActive: false });
    const totalUsers = await User.countDocuments({ role: 'student' }); // Only students count

    // Users active in the last 24 hours
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const activeToday = await User.countDocuments({
      lastActive: { $gte: yesterday },
      role: 'student'
    });

    // Recent courses (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentCourses = await Course.countDocuments({
      createdAt: { $gte: thirtyDaysAgo }
    });

    // Course engagement metrics
    const popularCourses = await Course.find({ isActive: true })
      .sort({ enrollmentCount: -1 })
      .limit(5)
      .select('title enrollmentCount');

    res.json({
      totalUsers, // Students only
      totalCourses,
      activeCourses,
      inactiveCourses,
      activeToday,
      recentCourses,
      popularCourses,
      role: 'admin' // Indicate this is admin-level data
    });
  } catch (error) {
    console.error('Error fetching admin statistics:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Toggle course active status (admin and super-admin)
router.patch('/courses/:id/toggle-status', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    const course = await Course.findById(req.params.id);

    if (!course) {
      return res.status(404).json({ message: 'Course not found' });
    }

    // Toggle the active status
    course.isActive = !course.isActive;
    course.updatedAt = new Date();
    await course.save();

    // Log the action
    console.log(`${req.user.role} ${req.user.email} ${course.isActive ? 'activated' : 'deactivated'} course: ${course.title}`);

    res.json({
      message: `Course ${course.isActive ? 'activated' : 'deactivated'} successfully`,
      course: course
    });
  } catch (error) {
    console.error('Error toggling course status:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get course analytics (admin and super-admin)
router.get('/courses/:id/analytics', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    const course = await Course.findById(req.params.id);

    if (!course) {
      return res.status(404).json({ message: 'Course not found' });
    }

    // Basic analytics (in a real app, you'd have more detailed tracking)
    const analytics = {
      courseId: course._id,
      title: course.title,
      isActive: course.isActive,
      createdAt: course.createdAt,
      updatedAt: course.updatedAt,
      enrollmentCount: course.enrollmentCount || 0,
      // Add more analytics as needed
      views: course.views || 0,
      completionRate: course.completionRate || 0
    };

    res.json(analytics);
  } catch (error) {
    console.error('Error fetching course analytics:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get admin activity summary
router.get('/my-activity', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    const adminId = req.user._id;

    // Get courses created by this admin (if you track creator)
    const coursesCreated = await Course.countDocuments({ createdBy: adminId });

    // Get recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentCoursesCreated = await Course.countDocuments({
      createdBy: adminId,
      createdAt: { $gte: thirtyDaysAgo }
    });

    res.json({
      adminInfo: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: req.user.role
      },
      activity: {
        totalCoursesCreated: coursesCreated,
        recentCoursesCreated: recentCoursesCreated,
        lastLogin: req.user.lastActive
      }
    });
  } catch (error) {
    console.error('Error fetching admin activity:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

export default router;
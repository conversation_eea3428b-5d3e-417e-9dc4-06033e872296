import express from 'express';
import User from '../models/User.js';
import Course from '../models/Course.js';
import { verifyToken } from '../middleware/verifyToken.js';
import { allowRoles } from '../middleware/adminOnly.js';

const router = express.Router();

// Get all users (admin and super-admin only)
router.get('/users', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    const users = await User.find().select('-password').sort({ createdAt: -1 });
    res.json(users);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Update user role (admin and super-admin only)
router.put('/users/:id/role', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    const { role } = req.body;
    
    if (!['student', 'admin', 'instance', 'super-admin'].includes(role)) {
      return res.status(400).json({ message: 'Invalid role' });
    }

    const user = await User.findByIdAndUpdate(
      req.params.id,
      { role },
      { new: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json(user);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Delete user (super-admin only)
router.delete('/users/:id', verifyToken, allowRoles('super-admin'), async (req, res) => {
  try {
    const user = await User.findByIdAndDelete(req.params.id);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get all courses for admin (including inactive)
router.get('/courses', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    const courses = await Course.find().sort({ createdAt: -1 });
    res.json(courses);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get dashboard statistics
router.get('/statistics', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    const totalUsers = await User.countDocuments();
    const totalCourses = await Course.countDocuments({ isActive: true });
    const totalAdmins = await User.countDocuments({ role: 'admin' });
    
    // Users active in the last 24 hours
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const activeToday = await User.countDocuments({ 
      lastActive: { $gte: yesterday } 
    });

    // Recent courses (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentCourses = await Course.countDocuments({
      createdAt: { $gte: thirtyDaysAgo },
      isActive: true
    });

    res.json({
      totalUsers,
      totalCourses,
      totalAdmins,
      activeToday,
      recentCourses
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

export default router;
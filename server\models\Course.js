import mongoose from 'mongoose';


// مخطط الدرس
const lessonSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ['video', 'reading', 'exercise'],
    required: true
  },
  content: {
    type: String // يمكن أن يكون رابط فيديو أو نص
  },
  videoUrl: String,
  textContent: String
}, { _id: false });

// مخطط السؤال للاختبار
const questionSchema = new mongoose.Schema({
  question: { type: String, required: true },
  options: [{ type: String, required: true }],
  correctAnswer: { type: String, required: true }
}, { _id: false });

// مخطط الاختبار
const quizSchema = new mongoose.Schema({
  questions: [questionSchema]
}, { _id: false });

// مخطط الوحدة
const unitSchema = new mongoose.Schema({
  title: { type: String, required: true },
  lessons: [lessonSchema],
  quiz: quizSchema
}, { _id: false });

const courseSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  video: {
    type: String,

  },
  image: {
    type: String, // مسار الصورة
  },
  instructor: {
    type: String,
    default: 'Platform Instructor'
  },
  duration: {
    type: String,
    default: '0 mins'
  },
  level: {
    type: String,
    enum: ['Beginner', 'Intermediate', 'Advanced'],
    default: 'Beginner'
  },
  tags: [{
    type: String,
    trim: true
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  enrollment: {
    type: Number,
    default: 0
  },
  enrollmentCount: {
    type: Number,
    default: 0
  },
  views: {
    type: Number,
    default: 0
  },
  completionRate: {
    type: Number,
    default: 0
  },
  rating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  ratingCount: {
    type: Number,
    default: 0
  },
  units: [unitSchema] // الوحدات
}, {
  timestamps: true
});

courseSchema.index({ title: 'text', description: 'text', tags: 'text' });

export default mongoose.model('Course', courseSchema);
import mongoose from 'mongoose';

// مخطط الدرس
const lessonSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ['video', 'reading', 'exercise'],
    required: true
  },
  content: {
    type: String // يمكن أن يكون رابط فيديو أو نص
  },
  videoUrl: String,
  textContent: String
}, { _id: false });

// مخطط السؤال للاختبار
const questionSchema = new mongoose.Schema({
  question: { type: String, required: true },
  options: [{ type: String, required: true }],
  correctAnswer: { type: String, required: true }
}, { _id: false });

// مخطط الاختبار
const quizSchema = new mongoose.Schema({
  questions: [questionSchema]
}, { _id: false });

// مخطط الوحدة
const unitSchema = new mongoose.Schema({
  title: { type: String, required: true },
  lessons: [lessonSchema],
  quiz: quizSchema
}, { _id: false });

const courseSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  video: {
    type: String,
    
  },
  instructor: {
    type: String,
    default: 'Platform Instructor'
  },
  duration: {
    type: String,
    default: '0 mins'
  },
  level: {
    type: String,
    enum: ['Beginner', 'Intermediate', 'Advanced'],
    default: 'Beginner'
  },
  tags: [{
    type: String,
    trim: true
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  units: [unitSchema] // الوحدات
}, {
  timestamps: true
});

courseSchema.index({ title: 'text', description: 'text', tags: 'text' });

export default mongoose.model('Course', courseSchema);
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import 'bootstrap/dist/css/bootstrap.min.css';
import { AuthProvider } from './context/AuthContext.jsx';
import { ThemeProvider } from './context/ThemeContext.jsx';
import AppNavbar from './components/Navbar.jsx';
import ProtectedRoute from './components/ProtectedRoute.jsx';
import Chatbot from './components/Chatbot.jsx';
import AdminLayout from './pages/Admin/AdminLayout.jsx';


// Pages
import Home from './pages/Home.jsx';
import Login from './pages/Login.jsx';
import Register from './pages/Register.jsx';
import Courses from './pages/Courses.jsx';
import CourseDetail from './pages/CourseDetail.jsx';
import StudentCourse from './pages/StudentCourse.jsx';
import HonorBoard from './pages/HonorBoard.jsx';
import PublicProfile from './pages/PublicProfile.jsx';

// Admin Pages
import Dashboard from './pages/Admin/Dashboard.jsx';
import ManageCourses from './pages/Admin/ManageCourses.jsx';
import ManageUsers from './pages/Admin/ManageUsers.jsx';
import AddCourse from './pages/Admin/AddCourse.jsx';
import Profile from './pages/Profile.jsx';
import UpdateCourse from './pages/Admin/UpdateCourse.jsx';

function App() {
  const token =localStorage.getItem('token');
  return (
    <ThemeProvider>
      <AuthProvider token={token}>
        <Router>
          <div className="min-vh-100">
            <AppNavbar />
            
            <Routes>
              {/* Public Routes */}
              <Route path="/" element={<Home />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={ <Register />} />
              <Route path="/profile" element={token ? <Profile /> : <Login />} />

                 {/* Public User Profile */}
              <Route path="/profile/:id" element={token ? <PublicProfile /> : <Login />} />
              
              {/* Public Course Routes - No login required for preview */}
              <Route path="/courses" element={<Courses />} />
              <Route path="/courses/:id" element={<CourseDetail />} />
              <Route path="/courses/:id/learn" element={
                <ProtectedRoute>
                  <StudentCourse />
                </ProtectedRoute>
              } />
              <Route path="/honor-board" element={<HonorBoard />} />

           
              

              

              {/* Admin Routes */}
              <Route path="/admin" element={
                <ProtectedRoute adminOnly>
                  <AdminLayout>
                    <Dashboard />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/courses" element={
                <ProtectedRoute adminOnly>
                  <AdminLayout>
                    <ManageCourses />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/users" element={
                <ProtectedRoute adminOnly>
                  <AdminLayout>
                    <ManageUsers />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/add-course" element={
                <ProtectedRoute adminOnly>
                  <AdminLayout>
                    <AddCourse />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/edit-course/:id" element={
                <ProtectedRoute adminOnly>
                  <AdminLayout>
                    <UpdateCourse />
                  </AdminLayout>
                </ProtectedRoute>
              } />

              {/* Catch-all for 404 */}
              {/* <Route path="*" element={<h1 className="text-center mt-5">404 - Page Not Found</h1>} /> */}
            </Routes>

            {/* Chatbot Widget - Only for authenticated users */}
            <Chatbot />
          </div>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
import React from 'react';
import LessonForm from './LessonForm';
import QuizForm from './QuizForm';

const UnitForm = ({ unit, onChange, onRemove, unitIndex }) => {
  // إدارة الدروس
  const handleAddLesson = () => {
    onChange({ ...unit, lessons: [...(unit.lessons || []), { type: 'video', videoUrl: '', textContent: '' }] });
  };
  const handleLessonChange = (idx, updatedLesson) => {
    const newLessons = unit.lessons.map((l, i) => (i === idx ? updatedLesson : l));
    onChange({ ...unit, lessons: newLessons });
  };
  const handleRemoveLesson = (idx) => {
    onChange({ ...unit, lessons: unit.lessons.filter((_, i) => i !== idx) });
  };

  // إدارة الاختبار
  const handleQuizChange = (updatedQuiz) => {
    onChange({ ...unit, quiz: updatedQuiz });
  };

  return (
    <div className="border rounded p-3 mb-3">
      <div className="d-flex justify-content-between align-items-center mb-2">
        <h5>الوحدة {unitIndex + 1}</h5>
        <button type="button" className="btn btn-danger btn-sm" onClick={onRemove}>
          حذف الوحدة
        </button>
      </div>
      <div className="mb-2">
        <label className="form-label">عنوان الوحدة</label>
        <input
          type="text"
          className="form-control"
          value={unit.title}
          onChange={e => onChange({ ...unit, title: e.target.value })}
          placeholder="أدخل عنوان الوحدة"
          required
        />
      </div>
      {/* إدارة الدروس */}
      <div className="mb-2">
        <div className="d-flex justify-content-between align-items-center mb-1">
          <span className="fw-bold">الدروس</span>
          <button type="button" className="btn btn-outline-success btn-sm" onClick={handleAddLesson}>
            إضافة درس
          </button>
        </div>
        {(!unit.lessons || unit.lessons.length === 0) && <div className="text-muted">لم يتم إضافة أي درس بعد.</div>}
        {unit.lessons && unit.lessons.map((lesson, idx) => (
          <LessonForm
            key={idx}
            lesson={lesson}
            lessonIndex={idx}
            onChange={updated => handleLessonChange(idx, updated)}
            onRemove={() => handleRemoveLesson(idx)}
          />
        ))}
      </div>
      {/* إدارة الاختبار */}
      <QuizForm quiz={unit.quiz || { questions: [] }} onChange={handleQuizChange} />
    </div>
  );
};

export default UnitForm; 
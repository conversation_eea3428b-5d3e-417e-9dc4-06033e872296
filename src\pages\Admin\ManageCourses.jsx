import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON>, <PERSON><PERSON>, <PERSON>, Al<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Modal,
  Container
} from 'react-bootstrap';
import { Eye, Edit, Trash2, <PERSON>Off, Copy, Archive, BarChart3 } from 'lucide-react';
import { coursesAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';

const ManageCourses = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [courseToDelete, setCourseToDelete] = useState(null);
  const [showToggleModal, setShowToggleModal] = useState(false);
  const [courseToToggle, setCourseToToggle] = useState(null);

  const navigate = useNavigate();
  const { user, isSuperAdmin } = useAuth();

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const response = await coursesAPI.getAll();
      setCourses(response.data);
    } catch (err) {
      setError('فشل في تحميل الدورات');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClick = (course) => {
    setCourseToDelete(course);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await coursesAPI.delete(courseToDelete._id);
      setShowDeleteModal(false);
      setCourseToDelete(null);
      fetchCourses();
    } catch (err) {
      setError('فشل في حذف الدورة');
    }
  };

  const handleToggleClick = (course) => {
    setCourseToToggle(course);
    setShowToggleModal(true);
  };

  const handleToggleConfirm = async () => {
    try {
      await coursesAPI.toggleStatus(courseToToggle._id);
      setShowToggleModal(false);
      setCourseToToggle(null);
      fetchCourses();
    } catch (err) {
      setError('فشل في تغيير حالة الدورة');
    }
  };

  const handleEditClick = (courseId) => {
    navigate(`/admin/edit-course/${courseId}`);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">جاري التحميل...</span>
        </Spinner>
      </div>
    );
  }

  return (
    <Container fluid className="py-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="fw-bold">إدارة الدورات</h2>
          <p className="text-muted mb-0">
            إدارة الدورات مع إمكانية التعديل والحذف
          </p>
        </div>
        <div className="d-flex gap-2">
          <Button variant="primary" onClick={() => navigate('/admin/add-course')}>
            اضافة دورة
          </Button>
        </div>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}

      <Card>
        <Card.Body>
          {courses.length === 0 ? (
            <Alert variant="info">
              لا توجد دورات متاحة حالياً.
            </Alert>
          ) : (
            <Table responsive striped hover>
              <thead>
                <tr>
                  <th>العنوان</th>
                  <th>المدرب</th>
                  <th>المستوى</th>
                  <th>المدة</th>
                  <th>الحالة</th>
                  <th>تاريخ الإنشاء</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {courses.map((course) => (
                  <tr key={course._id}>
                    <td>
                      <div>
                        <strong>{course.title}</strong>
                        <br />
                        <small className="text-muted">
                          {course.description?.substring(0, 50)}...
                        </small>
                      </div>
                    </td>
                    <td>{course.instructor}</td>
                    <td>
                      <Badge bg="secondary">{course.level}</Badge>
                    </td>
                    <td>{course.duration}</td>
                    <td>
                      <Badge bg={course.isActive ? 'success' : 'secondary'}>
                        {course.isActive ? 'مفعلة' : 'غير مفعلة'}
                      </Badge>
                    </td>
                    <td>
                      {new Date(course.createdAt).toLocaleDateString()}
                    </td>
                    <td>
                      <div className="d-flex gap-1">
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => navigate(`/courses/${course._id}`)}
                          title="عرض الدورة"
                        >
                          <Eye size={16} />
                        </Button>

                        <Button
                          variant="outline-warning"
                          size="sm"
                          onClick={() => handleEditClick(course._id)}
                          title="تعديل الدورة"
                        >
                          <Edit size={16} />
                        </Button>

                        <Button
                          variant={course.isActive ? 'outline-secondary' : 'outline-success'}
                          size="sm"
                          onClick={() => handleToggleClick(course)}
                          title={course.isActive ? 'إلغاء التفعيل' : 'تفعيل'}
                        >
                          {course.isActive ? <EyeOff size={16} /> : <Eye size={16} />}
                        </Button>

                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() => handleDeleteClick(course)}
                          title="حذف الدورة"
                        >
                          <Trash2 size={16} />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Delete Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>حذف الدورة</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          هل أنت متأكد من حذف الدورة "{courseToDelete?.title}"؟
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            إلغاء
          </Button>
          <Button variant="danger" onClick={handleDeleteConfirm}>
            حذف
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Toggle Status Modal */}
      <Modal show={showToggleModal} onHide={() => setShowToggleModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>تغيير حالة الدورة</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          هل تريد {courseToToggle?.isActive ? 'إلغاء تفعيل' : 'تفعيل'} الدورة "{courseToToggle?.title}"؟
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowToggleModal(false)}>
            إلغاء
          </Button>
          <Button variant="primary" onClick={handleToggleConfirm}>
            {courseToToggle?.isActive ? 'إلغاء التفعيل' : 'تفعيل'}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default ManageCourses;

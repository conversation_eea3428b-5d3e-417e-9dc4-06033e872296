import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, Alert, Badge, Modal } from 'react-bootstrap';
import { adminAPI, coursesAPI } from '../../services/api';
import { Edit, Trash2, Eye } from 'lucide-react';

const ManageCourses = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [courseToDelete, setCourseToDelete] = useState(null);

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      const response = await adminAPI.getCourses();
      setCourses(response.data);
    } catch (err) {
      setError('Failed to load courses');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClick = (course) => {
    setCourseToDelete(course);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await coursesAPI.delete(courseToDelete._id);
      setShowDeleteModal(false);
      setCourseToDelete(null);
      fetchCourses(); // Refresh the list
    } catch (err) {
      setError('Failed to delete course');
    }
  };

  const getLevelBadgeVariant = (level) => {
    switch (level) {
      case 'Beginner': return 'success';
      case 'Intermediate': return 'warning';
      case 'Advanced': return 'danger';
      default: return 'secondary';
    }
  };

  if (loading) {
    return (
      <div className="text-center py-5">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2 className="fw-bold">Manage Courses</h2>
        <Button variant="primary" href="/admin/add-course">
          Add New Course
        </Button>
      </div>

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      <div className="bg-white rounded shadow-sm">
        <Table responsive hover className="mb-0">
          <thead className="bg-light">
            <tr>
              <th>Title</th>
              <th>Instructor</th>
              <th>Level</th>
              <th>Duration</th>
              <th>Status</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {courses.map((course) => (
              <tr key={course._id}>
                <td className="fw-semibold">{course.title}</td>
                <td>{course.instructor}</td>
                <td>
                  <Badge bg={getLevelBadgeVariant(course.level)}>
                    {course.level}
                  </Badge>
                </td>
                <td>{course.duration}</td>
                <td>
                  <Badge bg={course.isActive ? 'success' : 'secondary'}>
                    {course.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </td>
                <td>{new Date(course.createdAt).toLocaleDateString()}</td>
                <td>
                  <div className="d-flex gap-2">
                    <Button
                      variant="outline-primary"
                      size="sm"
                      href={`/courses/${course._id}`}
                      target="_blank"
                    >
                      <Eye size={16} />
                    </Button>
                    <Button
                      variant="outline-warning"
                      size="sm"
                      href={`/admin/edit-course/${course._id}`}
                    >
                      <Edit size={16} />
                    </Button>
                    <Button
                      variant="outline-danger"
                      size="sm"
                      onClick={() => handleDeleteClick(course)}
                    >
                      <Trash2 size={16} />
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
        
        {courses.length === 0 && (
          <div className="text-center py-5 text-muted">
            <h5>No courses found</h5>
            <p>Start by adding your first course</p>
            <Button variant="primary" href="/admin/add-course">
              Add New Course
            </Button>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Delete Course</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Are you sure you want to delete the course "{courseToDelete?.title}"? 
          This action cannot be undone.
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleDeleteConfirm}>
            Delete Course
          </Button>
          only to check if the course is active
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default ManageCourses;
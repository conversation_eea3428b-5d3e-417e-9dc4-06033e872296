import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>ert, <PERSON>, Modal } from 'react-bootstrap';
import { adminAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import { coursesAPI } from '../../services/api';      
import CourseActions from '../../components/CourseActions';
import { Eye, Edit, Trash2, EyeOff, Power, Copy, Archive, BarChart3 } from 'lucide-react';
import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

const ManageCourses = () => {
  const { isSuperAdmin } = useAuth();
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [courseToDelete, setCourseToDelete] = useState(null);
  const [showToggleModal, setShowToggleModal] = useState(false);
  const [courseToToggle, setCourseToToggle] = useState(null);
  const [showDuplicateModal, setShowDuplicateModal] = useState(false);
  const [courseToDuplicate, setCourseToDuplicate] = useState(null);
  const [stats, setStats] = useState(null);
  const [showStats, setShowStats] = useState(false);

  const navigate = useNavigate();


  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = useCallback(async () => {
  try {
    const response = await adminAPI.getCourses();
    setCourses(response.data);
  } catch (err) {
    setError('فشل في تحميل الدورات');
  } finally {
    setLoading(false);
  }
}, []);
  const handleDeleteClick = (course) => {
    setCourseToDelete(course);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      if (isSuperAdmin) {
        await coursesAPI.permanentDelete(courseToDelete._id);
      } else {
        await coursesAPI.softDelete(courseToDelete._id);
      }
      setShowDeleteModal(false);
      setCourseToDelete(null);
      fetchCourses(); // Refresh the list
    } catch (err) {
      setError('فشل في حذف الدورة');
    }
  };

  const handleToggleClick = (course) => {
    setCourseToToggle(course);
    setShowToggleModal(true);
  };

  const handleToggleConfirm = async () => {
    try {
      await coursesAPI.toggleStatus(courseToToggle._id);
      setShowToggleModal(false);
      setCourseToToggle(null);
      fetchCourses(); // Refresh the list
    } catch (err) {
      setError('فشل في تغيير حالة الدورة');
    }
  };

  // دالة نسخ الدورة
  const handleDuplicateClick = (course) => {
    setCourseToDuplicate(course);
    setShowDuplicateModal(true);
  };

  const handleDuplicateConfirm = async () => {
    try {
      await coursesAPI.duplicate(courseToDuplicate._id);
      setShowDuplicateModal(false);
      setCourseToDuplicate(null);
      fetchCourses(); // Refresh the list
    } catch (err) {
      setError('فشل في نسخ الدورة');
    }
  };

  // دالة تحميل الإحصائيات
  const fetchStats = async () => {
    try {
      const response = await coursesAPI.getStats();
      setStats(response.data);
      setShowStats(true);
    } catch (err) {
      setError('فشل في تحميل الإحصائيات');
    }
  };

  // دالة التعديل
  const handleEditClick = (courseId) => {
    navigate(`/admin/edit-course/${courseId}`);
  };

  const getLevelBadgeVariant = (level) => {
    switch (level) {
      case 'Beginner': return 'success';
      case 'Intermediate': return 'warning';
      case 'Advanced': return 'danger';
      default: return 'secondary';
    }
  };

  if (loading) {
    return (
      <div className="text-center py-5">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="fw-bold">إدارة الدورات</h2>
          <p className="text-muted mb-0">
            {isSuperAdmin ? 'إدارة كاملة للدورات مع إمكانية الحذف النهائي' : 'إدارة الدورات مع إمكانية التعديل والإخفاء'}
          </p>
        </div>
        <div className="d-flex gap-2">
          <Button variant="outline-info" onClick={fetchStats}>
            <BarChart3 size={16} className="me-1" />
            الإحصائيات
          </Button>
          <Button variant="primary" onClick={() => navigate('/admin/add-course')}>
            اضافة دورة
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      <div className="bg-white rounded shadow-sm">
        <Table responsive hover className="mb-0">
          <thead className="bg-light">
            <tr>
              <th>عنوان الدورة</th>
              <th>المدرب</th>
              <th>المستوى</th>
              <th>المدة</th>
              <th>الحالة</th>
              <th>تاريخ الإنشاء</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {courses.map((course) => (
              <tr key={course._id}>
                <td className="fw-semibold">{course.title}</td>
                <td>{course.instructor}</td>
                <td>
                  <Badge bg={getLevelBadgeVariant(course.level)}>
                    {course.level}
                  </Badge>
                </td>
                <td>{course.duration}</td>
                <td>
                  <Badge bg={course.isActive ? 'success' : 'secondary'}>
                    {course.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </td>
                <td>{new Date(course.createdAt).toLocaleDateString()}</td>
                <td>
                  <div className="d-flex gap-2">
                    {/* View Course - Available to all admins */}
                    <Button
                      variant="outline-primary"
                      size="sm"
                      href={`/courses/${course._id}`}
                      target="_blank"
                      title="عرض الدورة"
                    >
                      <Eye size={16} />
                    </Button>

                    {/* Edit Course - Available to all admins */}
                    <Button
                      variant="outline-warning"
                      size="sm"
                      onClick={() => handleEditClick(course._id)}
                      title="تعديل الدورة"
                    >
                      <Edit size={16} />
                    </Button>

                    {/* Duplicate Course - Available to all admins */}
                    <Button
                      variant="outline-info"
                      size="sm"
                      onClick={() => handleDuplicateClick(course)}
                      title="نسخ الدورة"
                    >
                      <Copy size={16} />
                    </Button>

                    {/* Toggle Active Status - Available to all admins */}
                    <Button
                      variant={course.isActive ? "outline-secondary" : "outline-success"}
                      size="sm"
                      onClick={() => handleToggleClick(course)}
                      title={course.isActive ? "إخفاء الدورة" : "إظهار الدورة"}
                    >
                      {course.isActive ? <EyeOff size={16} /> : <Power size={16} />}
                    </Button>

                    {/* Delete Course - Super Admin Only */}
                    {isSuperAdmin && (
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={() => handleDeleteClick(course)}
                        title="حذف الدورة نهائياً"
                      >
                        <Trash2 size={16} />
                      </Button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
        
        {courses.length === 0 && (
          <div className="text-center py-5 text-muted">
            <h5>لا توجد دورات</h5>
            <p>ابدأ بإضافة أول دورة</p>
            <Button variant="primary" href="/admin/add-course">
              إضافة دورة جديدة
            </Button>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal - Super Admin Only */}
      {isSuperAdmin && (
        <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
          <Modal.Header closeButton>
            <Modal.Title className="text-danger">حذف الدورة نهائياً</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div className="alert alert-danger">
              <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
            </div>
            هل أنت متأكد من حذف الدورة "{courseToDelete?.title}" نهائياً؟
            <br />
            سيتم حذف جميع البيانات المرتبطة بهذه الدورة.
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
              إلغاء
            </Button>
            <Button variant="danger" onClick={handleDeleteConfirm}>
              حذف نهائياً
            </Button>
          </Modal.Footer>
        </Modal>
      )}

      {/* Toggle Course Status Modal */}
      <Modal show={showToggleModal} onHide={() => setShowToggleModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>
            {courseToToggle?.isActive ? 'إخفاء الدورة' : 'إظهار الدورة'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          هل تريد {courseToToggle?.isActive ? 'إخفاء' : 'إظهار'} الدورة "{courseToToggle?.title}"؟
          <br />
          <small className="text-muted">
            {courseToToggle?.isActive
              ? 'سيتم إخفاء الدورة من الطلاب ولكن ستبقى البيانات محفوظة'
              : 'سيتم إظهار الدورة للطلاب مرة أخرى'
            }
          </small>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowToggleModal(false)}>
            إلغاء
          </Button>
          <Button
            variant={courseToToggle?.isActive ? "warning" : "success"}
            onClick={handleToggleConfirm}
          >
            {courseToToggle?.isActive ? 'إخفاء الدورة' : 'إظهار الدورة'}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

      {/* Duplicate Course Modal */}
      <Modal show={showDuplicateModal} onHide={() => setShowDuplicateModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>نسخ الدورة</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          هل تريد إنشاء نسخة من الدورة "{courseToDuplicate?.title}"؟
          <br />
          <small className="text-muted">
            سيتم إنشاء نسخة جديدة من الدورة بجميع محتوياتها، وستكون غير مفعلة افتراضياً.
          </small>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDuplicateModal(false)}>
            إلغاء
          </Button>
          <Button variant="info" onClick={handleDuplicateConfirm}>
            نسخ الدورة
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Statistics Modal */}
      <Modal show={showStats} onHide={() => setShowStats(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>إحصائيات الدورات</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {stats && (
            <div>
              <div className="row mb-4">
                <div className="col-md-4">
                  <div className="card text-center">
                    <div className="card-body">
                      <h3 className="text-primary">{stats.overview.total}</h3>
                      <p className="mb-0">إجمالي الدورات</p>
                    </div>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="card text-center">
                    <div className="card-body">
                      <h3 className="text-success">{stats.overview.active}</h3>
                      <p className="mb-0">دورات مفعلة</p>
                    </div>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="card text-center">
                    <div className="card-body">
                      <h3 className="text-secondary">{stats.overview.inactive}</h3>
                      <p className="mb-0">دورات غير مفعلة</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="row">
                <div className="col-md-6">
                  <h5>الدورات حسب المستوى</h5>
                  <ul className="list-group">
                    {stats.levelStats.map((level, index) => (
                      <li key={index} className="list-group-item d-flex justify-content-between">
                        <span>{level._id}</span>
                        <Badge bg="primary">{level.count}</Badge>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="col-md-6">
                  <h5>أفضل المدربين</h5>
                  <ul className="list-group">
                    {stats.instructorStats.slice(0, 5).map((instructor, index) => (
                      <li key={index} className="list-group-item d-flex justify-content-between">
                        <span>{instructor._id}</span>
                        <Badge bg="success">{instructor.count}</Badge>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div className="mt-4">
                <h5>أحدث الدورات</h5>
                <div className="list-group">
                  {stats.recentCourses.map((course, index) => (
                    <div key={index} className="list-group-item">
                      <div className="d-flex justify-content-between align-items-center">
                        <div>
                          <h6 className="mb-1">{course.title}</h6>
                          <small className="text-muted">المدرب: {course.instructor}</small>
                        </div>
                        <div className="text-end">
                          <Badge bg={course.isActive ? 'success' : 'secondary'}>
                            {course.isActive ? 'مفعلة' : 'غير مفعلة'}
                          </Badge>
                          <br />
                          <small className="text-muted">
                            {new Date(course.createdAt).toLocaleDateString()}
                          </small>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowStats(false)}>
            إغلاق
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default ManageCourses;
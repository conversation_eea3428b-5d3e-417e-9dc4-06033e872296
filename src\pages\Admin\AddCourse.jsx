import React, { useState } from 'react';
import { Card, Form, Button, Alert, Row, Col } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { coursesAPI } from '../../services/api';
import { Save } from 'lucide-react';
import UnitForm from '../../components/UnitForm';

const AddCourse = () => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    instructor: '',
    duration: '',
    level: 'Beginner',
    tags: ''
  });
  const [units, setUnits] = useState([]); // مصفوفة الوحدات
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [videoFile, setVideoFile] = useState(null);
  const [videoUrl, setVideoUrl] = useState('');
  const [textContent, setTextContent] = useState('');
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  // إدارة الوحدات
  const handleAddUnit = () => {
    setUnits([...units, { title: '', lessons: [], quiz: { questions: [] } }]);
  };
  const handleUnitChange = (idx, updatedUnit) => {
    setUnits(units.map((u, i) => (i === idx ? updatedUnit : u)));
  };
  const handleRemoveUnit = (idx) => {
    setUnits(units.filter((_, i) => i !== idx));
  };

const handleSubmit = async (e) => {
  e.preventDefault();
  setError('');
  setLoading(true);
  console.log('formData:', formData);
  console.log('Submit' );
  if (!formData.title || !formData.description || !formData.instructor || !formData.duration || !formData.level) {
  setError('يرجى تعبئة جميع الحقول المطلوبة.');
  setLoading(false);
  return;
}
if (units.length === 0) {
  setError('يجب إضافة وحدة واحدة على الأقل.');
  setLoading(false);
  return;
}
  

  try {
    const tagsArray = formData.tags
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);

    // 1️⃣ تجهيز FormData
    const formPayload = new FormData();
    formPayload.append('title', formData.title);
    formPayload.append('description', formData.description);
    formPayload.append('instructor', formData.instructor);
    formPayload.append('duration', formData.duration);
    formPayload.append('level', formData.level);
    formPayload.append('tags', tagsArray.join(',')); // backend يفكها لاحقًا

    if (videoFile) {
      formPayload.append('video', videoFile); // 2️⃣ أضف الفيديو للـ FormData
    }

    // 3️⃣ أضف الوحدات
    formPayload.append('units', JSON.stringify(units)); // تأكد من أن backend يقبل هذا

    // 4️⃣ أرسل الطلب عبر Axios
    await coursesAPI.create(formPayload);

    navigate('/admin/courses');
  } catch (err) {
    setError(err.response?.data?.message || 'فشل في إنشاء الدورة');
  } finally {
    setLoading(false);
  }
};



  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2 className="fw-bold">إضافة دورة جديدة</h2>
      </div>

      <Card className="shadow-sm border-0">
        <Card.Body className="p-4">
          {error && (
            <Alert variant="danger" className="mb-4">
              {error}
            </Alert>
          )}

          <Form onSubmit={handleSubmit}>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>عنوان الدورة *</Form.Label>
                  <Form.Control
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    required
                    placeholder="أدخل عنوان الدورة"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>المدرب</Form.Label>
                  <Form.Control
                    type="text"
                    name="instructor"
                    value={formData.instructor}
                    onChange={handleChange}
                    placeholder="أدخل اسم المدرب"
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>الوصف *</Form.Label>
              <Form.Control
                as="textarea"
                rows={4}
                name="description"
                value={formData.description}
                onChange={handleChange}
                required
                placeholder="أدخل وصف الدورة"
              />
              {/* add video */}
           <Form.Group className="mb-3">
            <Form.Label>إضافة فيديو</Form.Label>
            <Form.Control
              type="file"
              accept="video/*"
              name="video"
              onChange={(e) => {
                const file = e.target.files[0];
                if (file) {
                  setVideoFile(file);
                  const previewURL = URL.createObjectURL(file);
                  setVideoUrl(previewURL); // هنا حفظنا رابط العرض المسبق
                }
              }}
            />
            <Form.Text className="text-muted">
              يمكنك رفع فيديو تعريفي للدورة. يجب أن يكون بتنسيق MP4 أو WebM.
            </Form.Text>
          </Form.Group>
              {videoUrl && (
                <div className="mb-3">
                  <video
                    src={videoUrl}
                    controls
                    className="w-100"
                    style={{ maxHeight: '300px' }}
                  />
                </div>
              )}


            </Form.Group>
            <Row>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>المدة</Form.Label>
                  <Form.Control
                    type="text"
                    name="duration"
                    value={formData.duration}
                    onChange={handleChange}
                    placeholder="مثال: ساعتان"
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>المستوى</Form.Label>
                  <Form.Select
                    name="level"
                    value={formData.level}
                    onChange={handleChange}
                  >
                    <option value="Beginner">مبتدئ</option>
                    <option value="Intermediate">متوسط</option>
                    <option value="Advanced">متقدم</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>العلامات</Form.Label>
                  <Form.Control
                    type="text"
                    name="tags"
                    value={formData.tags}
                    onChange={handleChange}
                    placeholder="مثال: جافاسكريبت، ريأكت، تطوير الويب"
                  />
                  <Form.Text className="text-muted">
                    افصل العلامات بفواصل
                  </Form.Text>
                </Form.Group>
              </Col>
            </Row>

            {/* إدارة الوحدات */}
            <div className="mb-4">
              <div className="d-flex justify-content-between align-items-center mb-2">
                <h4 className="fw-bold mb-0">الوحدات</h4>
                <Button type="button" variant="success" onClick={handleAddUnit} size="sm" className="d-flex align-items-center">
                  إضافة وحدة
                </Button>
              </div>
              {units.length === 0 && <div className="text-muted">لم يتم إضافة أي وحدة بعد.</div>}
              {units.map((unit, idx) => (
                <UnitForm
                  key={idx}
                  unit={unit}
                  unitIndex={idx}
                  onChange={updated => handleUnitChange(idx, updated)}
                  onRemove={() => handleRemoveUnit(idx)}
                />
              ))}
            </div>

            <div className="d-flex gap-3">
              <Button 
                type="submit" 
                variant="primary" 
                disabled={loading}
                className="d-flex align-items-center"
                
              >
                {loading ? (
                  <>
                    <div className="spinner-border spinner-border-sm me-2" role="status">
                      <span className="visually-hidden">جاري التحميل...</span>
                    </div>
                    جاري إنشاء الدورة...
                  </>
                ) : (
                  <>
                    <Save size={16} className="me-2" />
                    إنشاء دورة
                  </>
                )}
              </Button>
              <Button 
                type="button" 
                variant="outline-secondary"
                onClick={() => navigate('/admin/courses')}
              >
                إلغاء
              </Button>
            </div>
          </Form>
        </Card.Body>
      </Card>
    </div>
  );
};

export default AddCourse;
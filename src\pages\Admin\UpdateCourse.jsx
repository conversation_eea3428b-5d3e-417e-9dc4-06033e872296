import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON>, Card, Spinner, Alert, Row, Col, ListGroup, InputGroup } from "react-bootstrap";
import { coursesAPI } from "../../services/api";
import { useAuth } from "../../context/AuthContext";
import { CheckCircle, XCircle, Trash2, Plus, Video, HelpCircle } from "lucide-react";

const UpdateCourse = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user, isSuperAdmin } = useAuth();
  const [course, setCourse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState("");
  const [successMsg, setSuccessMsg] = useState("");

  const isAllowed = user?.role === "admin" || isSuperAdmin;

  useEffect(() => {
    const fetchCourse = async () => {
      try {
        const response = await coursesAPI.getById(id);
        setCourse({ ...response.data, units: response.data.units || [] });
      } catch (err) {
        setError("فشل في تحميل بيانات الدورة");
      } finally {
        setLoading(false);
      }
    };

    fetchCourse();
  }, [id]);

  const handleChange = (e) => {
    setCourse({ ...course, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setUpdating(true);
    setError("");
    setSuccessMsg("");

    try {
      const formData = new FormData();
      formData.append("title", course.title);
      formData.append("description", course.description);
      formData.append("level", course.level);
      formData.append("duration", course.duration);
      formData.append("instructor", course.instructor);
      formData.append("updatedBy", user?._id);
      formData.append("updatedAt", new Date().toISOString());
      formData.append("units", JSON.stringify(course.units || []));
      if (course.image instanceof File) {
        formData.append("image", course.image);
      }

      await coursesAPI.update(id, formData);
      setSuccessMsg("تم تحديث الدورة بنجاح");
    } catch (err) {
      setError("حدث خطأ أثناء تحديث الدورة");
    } finally {
      setUpdating(false);
    }
  };

  const handleAddUnit = () => {
    setCourse({ ...course, units: [...(course.units || []), { title: "", lessons: [], quiz: {} }] });
  };

  const handleUnitChange = (index, value) => {
    const updatedUnits = [...course.units];
    updatedUnits[index].title = value;
    setCourse({ ...course, units: updatedUnits });
  };

  const handleDeleteUnit = (index) => {
    const updatedUnits = course.units.filter((_, i) => i !== index);
    setCourse({ ...course, units: updatedUnits });
  };

  const handleAddLesson = (unitIndex) => {
    const updatedUnits = [...course.units];
    updatedUnits[unitIndex].lessons.push({ type: 'video', content: '' });
    setCourse({ ...course, units: updatedUnits });
  };

  const handleLessonChange = (unitIndex, lessonIndex, key, value) => {
    const updatedUnits = [...course.units];
    updatedUnits[unitIndex].lessons[lessonIndex][key] = value;
    setCourse({ ...course, units: updatedUnits });
  };

  const handleAddQuiz = (unitIndex) => {
    const updatedUnits = [...course.units];
    updatedUnits[unitIndex].quiz = { questions: [] };
    setCourse({ ...course, units: updatedUnits });
  };

  const handleAddQuestion = (unitIndex) => {
    const updatedUnits = [...course.units];
    updatedUnits[unitIndex].quiz.questions.push({ question: '', options: ['', '', '', ''], correctAnswer: '' });
    setCourse({ ...course, units: updatedUnits });
  };

  if (!isAllowed) {
    return (
      <div className="text-center py-5">
        <XCircle size={48} className="text-danger mb-3" />
        <h3>غير مصرح لك بالدخول</h3>
        <p className="text-muted">فقط المدير والمدير العام يمكنهم تعديل الدورات</p>
        <Button variant="primary" onClick={() => navigate("/")}>العودة للصفحة الرئيسية</Button>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="text-center py-5">
        <Spinner animation="border" variant="primary" />
        <p className="mt-2">جاري تحميل بيانات الدورة...</p>
      </div>
    );
  }

  return (
    <Card className="shadow-sm p-4 mt-4">
      <h3 className="mb-4">تعديل بيانات الدورة</h3>

      {error && <Alert variant="danger">{error}</Alert>}
      {successMsg && (
        <Alert variant="success" className="d-flex align-items-center gap-2">
          <CheckCircle size={20} />
          {successMsg}
        </Alert>
      )}

      <Form onSubmit={handleSubmit}>
        <Row>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>عنوان الدورة</Form.Label>
              <Form.Control
                type="text"
                name="title"
                value={course.title}
                onChange={handleChange}
                required
              />
            </Form.Group>
          </Col>

          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>المدرب</Form.Label>
              <Form.Control
                type="text"
                name="instructor"
                value={course.instructor}
                onChange={handleChange}
                required
              />
            </Form.Group>
          </Col>
        </Row>

        <Form.Group className="mb-3">
          <Form.Label>وصف الدورة</Form.Label>
          <Form.Control
            as="textarea"
            name="description"
            rows={4}
            value={course.description}
            onChange={handleChange}
            required
          />
        </Form.Group>

        <Row>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>المستوى</Form.Label>
              <Form.Select
                name="level"
                value={course.level}
                onChange={handleChange}
                required
              >
                <option value="Beginner">مبتدئ</option>
                <option value="Intermediate">متوسط</option>
                <option value="Advanced">متقدم</option>
              </Form.Select>
            </Form.Group>
          </Col>

          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>المدة</Form.Label>
              <Form.Control
                type="text"
                name="duration"
                value={course.duration}
                onChange={handleChange}
                placeholder="مثال: 6 ساعات"
              />
            </Form.Group>
          </Col>

          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>تحديث صورة الدورة</Form.Label>
              <Form.Control
                type="file"
                name="image"
                onChange={(e) => setCourse({ ...course, image: e.target.files[0] })}
              />
            </Form.Group>
          </Col>
        </Row>

        <h5 className="mt-4">الوحدات التعليمية</h5>
        <ListGroup className="mb-3">
          {course.units && course.units.map((unit, unitIndex) => (
            <ListGroup.Item key={unitIndex} className="mb-3">
              <InputGroup className="mb-2">
                <Form.Control
                  type="text"
                  value={unit.title}
                  onChange={(e) => handleUnitChange(unitIndex, e.target.value)}
                  placeholder={`عنوان الوحدة ${unitIndex + 1}`}
                />
                <Button variant="outline-danger" onClick={() => handleDeleteUnit(unitIndex)}>
                  <Trash2 size={16} />
                </Button>
              </InputGroup>

              <Button
                variant="outline-success"
                className="me-2 mb-2"
                onClick={() => handleAddLesson(unitIndex)}
              >
                <Video size={16} className="me-1" /> إضافة درس
              </Button>

              <Button
                variant="outline-warning"
                className="me-2 mb-2"
                onClick={() => handleAddQuiz(unitIndex)}
              >
                <HelpCircle size={16} className="me-1" /> إضافة اختبار
              </Button>
            </ListGroup.Item>
          ))}
        </ListGroup>

        <Button variant="outline-primary" onClick={handleAddUnit} className="mb-4">
          <Plus size={16} className="me-2" /> إضافة وحدة جديدة
        </Button>

        <hr className="my-4" />

        <Button type="submit" variant="success" disabled={updating}>
          {updating ? "جارٍ الحفظ..." : "حفظ التغييرات"}
        </Button>
      </Form>
    </Card>
  );
};

export default UpdateCourse;

import React from 'react';

const LessonForm = ({ lesson, onChange, onRemove, lessonIndex }) => {
  return (
    <div className="border rounded p-2 mb-2">
      <div className="d-flex justify-content-between align-items-center mb-1">
        <span>الدرس {lessonIndex + 1}</span>
        <button type="button" className="btn btn-outline-danger btn-sm" onClick={onRemove}>
          حذف الدرس
        </button>
      </div>
      <div className="mb-1">
        <label className="form-label">نوع الدرس</label>
        <select
          className="form-select"
          value={lesson.type}
          onChange={e => onChange({ ...lesson, type: e.target.value })}
          required
        >
          <option value="video">فيديو</option>
          <option value="reading">قراءة</option>
          <option value="exercise">تطبيق عملي</option>
        </select>
      </div>
      {lesson.type === 'video' && (
        <div className="mb-1">
          <label className="form-label">رابط الفيديو</label>
          <input
            type="text"
            className="form-control"
            value={lesson.videoUrl || ''}
            onChange={e => onChange({ ...lesson, videoUrl: e.target.value })}
            placeholder="أدخل رابط الفيديو (YouTube أو مباشر)"
            required
          />
        </div>
      )}
      {lesson.type === 'reading' && (
        <div className="mb-1">
          <label className="form-label">النص التعليمي</label>
          <textarea
            className="form-control"
            value={lesson.textContent || ''}
            onChange={e => onChange({ ...lesson, textContent: e.target.value })}
            placeholder="أدخل نص الدرس"
            rows={2}
            required
          />
        </div>
      )}
      {/* تطبيق عملي يمكن توسيعه لاحقًا */}
    </div>
  );
};

export default LessonForm; 
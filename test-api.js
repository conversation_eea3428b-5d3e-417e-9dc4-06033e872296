// Test script for the new Super Admin API endpoints
import axios from 'axios';

const API_BASE = 'http://localhost:5000/api';

// Test data - using existing super admin
const testUser = {
  email: '<EMAIL>',
  password: 'your-actual-password' // You'll need to use the correct password
};

// Alternative test with the newly created admin
const testAdmin = {
  email: '<EMAIL>',
  password: 'password123'
};

let authToken = '';

async function login() {
  try {
    console.log('🔐 Testing login with admin user...');
    const response = await axios.post(`${API_BASE}/auth/login`, testAdmin);
    authToken = response.data.token;
    console.log('✅ Login successful');
    console.log('User role:', response.data.user.role);
    return response.data.user;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data?.message || error.message);
    console.log('Trying to create a test user...');
    return null;
  }
}

async function testSuperAdminEndpoints() {
  if (!authToken) {
    console.log('❌ No auth token available');
    return;
  }

  const headers = { Authorization: `Bearer ${authToken}` };

  try {
    // Test get users
    console.log('\n📋 Testing GET /super-admin/users...');
    const usersResponse = await axios.get(`${API_BASE}/super-admin/users`, { headers });
    console.log('✅ Users fetched successfully');
    console.log(`Found ${usersResponse.data.length} users`);

    // Test get statistics
    console.log('\n📊 Testing GET /super-admin/statistics...');
    const statsResponse = await axios.get(`${API_BASE}/super-admin/statistics`, { headers });
    console.log('✅ Statistics fetched successfully');
    console.log('Statistics:', JSON.stringify(statsResponse.data, null, 2));

    // Test role update (if there are users)
    if (usersResponse.data.length > 1) {
      const targetUser = usersResponse.data.find(u => u.role === 'student');
      if (targetUser) {
        console.log(`\n🔄 Testing PUT /super-admin/users/${targetUser._id}/role...`);
        try {
          const roleResponse = await axios.put(
            `${API_BASE}/super-admin/users/${targetUser._id}/role`,
            { role: 'admin' },
            { headers }
          );
          console.log('✅ Role update successful');
          console.log('Updated user:', roleResponse.data.user.name, '-> admin');
          
          // Revert the change
          await axios.put(
            `${API_BASE}/super-admin/users/${targetUser._id}/role`,
            { role: 'student' },
            { headers }
          );
          console.log('✅ Role reverted to student');
        } catch (error) {
          console.error('❌ Role update failed:', error.response?.data?.message || error.message);
        }
      }
    }

  } catch (error) {
    console.error('❌ Super admin endpoint test failed:', error.response?.data?.message || error.message);
  }
}

async function testRegularAdminEndpoints() {
  if (!authToken) {
    console.log('❌ No auth token available');
    return;
  }

  const headers = { Authorization: `Bearer ${authToken}` };

  try {
    // Test admin statistics
    console.log('\n📊 Testing GET /admin/statistics...');
    const statsResponse = await axios.get(`${API_BASE}/admin/statistics`, { headers });
    console.log('✅ Admin statistics fetched successfully');
    console.log('Admin Statistics:', JSON.stringify(statsResponse.data, null, 2));

    // Test admin courses
    console.log('\n📚 Testing GET /admin/courses...');
    const coursesResponse = await axios.get(`${API_BASE}/admin/courses`, { headers });
    console.log('✅ Admin courses fetched successfully');
    console.log(`Found ${coursesResponse.data.length} courses`);

  } catch (error) {
    console.error('❌ Admin endpoint test failed:', error.response?.data?.message || error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting API Tests...\n');
  
  const user = await login();
  if (!user) {
    console.log('❌ Cannot proceed without authentication');
    return;
  }

  if (user.role === 'super-admin') {
    await testSuperAdminEndpoints();
  } else {
    console.log('ℹ️ User is not super-admin, testing regular admin endpoints only');
  }
  
  await testRegularAdminEndpoints();
  
  console.log('\n✅ All tests completed!');
}

// Run the tests
runTests().catch(console.error);

// <PERSON><PERSON>t to create a super admin user for testing
import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';

// Connect to MongoDB
const MONGO_URI = 'mongodb+srv://admin:<EMAIL>/academy?retryWrites=true&w=majority';

const userSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: { type: String, enum: ['student', 'admin', 'super-admin'], default: 'student' },
  lastActive: { type: Date, default: Date.now },
  isActive: { type: Boolean, default: true },
  loginCount: { type: Number, default: 0 }
}, { timestamps: true });

const User = mongoose.model('User', userSchema);

async function createSuperAdmin() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGO_URI);
    console.log('✅ Connected to MongoDB');

    // Check if super admin already exists
    const existingSuperAdmin = await User.findOne({ role: 'super-admin' });
    if (existingSuperAdmin) {
      console.log('ℹ️ Super admin already exists:', existingSuperAdmin.email);
      console.log('User details:', {
        name: existingSuperAdmin.name,
        email: existingSuperAdmin.email,
        role: existingSuperAdmin.role,
        isActive: existingSuperAdmin.isActive
      });
      return existingSuperAdmin;
    }

    // Create super admin
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('password123', salt);

    const superAdmin = new User({
      name: 'Super Administrator',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'super-admin',
      isActive: true,
      lastActive: new Date(),
      loginCount: 0
    });

    await superAdmin.save();
    console.log('✅ Super admin created successfully!');
    console.log('Login credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');
    console.log('Role: super-admin');

    return superAdmin;

  } catch (error) {
    console.error('❌ Error creating super admin:', error.message);
    if (error.code === 11000) {
      console.log('ℹ️ User with this email already exists');
      // Try to find and return the existing user
      const existingUser = await User.findOne({ email: '<EMAIL>' });
      if (existingUser) {
        console.log('Found existing user:', existingUser.email, 'Role:', existingUser.role);
        return existingUser;
      }
    }
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Also create a regular admin for testing
async function createRegularAdmin() {
  try {
    await mongoose.connect(MONGO_URI);

    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    if (existingAdmin) {
      console.log('ℹ️ Regular admin already exists:', existingAdmin.email);
      return existingAdmin;
    }

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('password123', salt);

    const admin = new User({
      name: 'Regular Administrator',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      isActive: true,
      lastActive: new Date(),
      loginCount: 0
    });

    await admin.save();
    console.log('✅ Regular admin created successfully!');
    console.log('Login credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');
    console.log('Role: admin');

    return admin;

  } catch (error) {
    console.error('❌ Error creating regular admin:', error.message);
  } finally {
    await mongoose.disconnect();
  }
}

async function createTestUsers() {
  console.log('🚀 Creating test users...\n');
  
  await createSuperAdmin();
  console.log('');
  await createRegularAdmin();
  
  console.log('\n✅ Test users creation completed!');
}

createTestUsers().catch(console.error);

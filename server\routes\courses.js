import express from 'express';
import multer from 'multer';
import Course from '../models/Course.js';
import { verifyToken } from '../middleware/verifyToken.js';
import { allowRoles } from '../middleware/adminOnly.js';

const router = express.Router();

// Configure multer for video uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/videos/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + '.' + file.originalname.split('.').pop());
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 100 * 1024 * 1024 }, // 100MB limit
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('video/')) {
      cb(null, true);
    } else {
      cb(new Error('Only video files are allowed!'), false);
    }
  }
});

// Get all courses (public)
router.get('/', async (req, res) => {
  try {
    const { search, page = 1, limit = 10 } = req.query;
    const query = { isActive: true };

    if (search) {
      query.$text = { $search: search };
    }

    const courses = await Course.find(query)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Course.countDocuments(query);

    res.json({
      courses,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get single course
router.get('/:id', async (req, res) => {
  try {
    const course = await Course.findById(req.params.id);
    if (!course || !course.isActive) {
      return res.status(404).json({ message: 'Course not found' });
    }
    res.json(course);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Create course (admin and super-admin only)
router.post('/', verifyToken, allowRoles('admin', 'super-admin'), upload.single('video'), async (req, res) => {
  try {
    const { title, description, instructor, duration, level, tags, units } = req.body;

    const course = new Course({
      title,
      description,
      instructor,
      duration,
      level,
      tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
      video: req.file?.path,
      units: units ? JSON.parse(units) : [],
      createdBy: req.user.id
    });

    await course.save();
    res.status(201).json(course);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Update course (admin and super-admin only)
// Update course (admin and super-admin only)
router.put('/:id', verifyToken, allowRoles('admin', 'super-admin'), upload.single('video'), async (req, res) => {
  try {
    const {
      title,
      description,
      instructor,
      duration,
      level,
      tags,
      isActive
    } = req.body;

    const updateData = {
      title,
      description,
      instructor,
      duration,
      level,
      isActive: isActive === 'true',
      updatedBy: req.user.id,
      updatedAt: new Date()
    };

    // معالجة الفيديو إن وُجد
    if (req.file) {
      updateData.video = req.file.path;
    }

    // معالجة الوسوم
    if (tags) {
      updateData.tags = tags.split(',').map(t => t.trim());
    }

    // معالجة الوحدات والدروس إن وُجدت
    if (req.body.units) {
      updateData.units = JSON.parse(req.body.units);
    }

    const course = await Course.findByIdAndUpdate(req.params.id, updateData, { new: true });

    if (!course) {
      return res.status(404).json({ message: 'Course not found' });
    }

    res.json(course);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});


// Delete course (admin and super-admin only)
router.delete('/:id', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    const course = await Course.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );

    if (!course) {
      return res.status(404).json({ message: 'Course not found' });
    }

    res.json({ message: 'Course deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

export default router;
import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Alert } from 'react-bootstrap';
import { adminAPI } from '../../services/api';
import { Users, BookOpen, UserCheck, Activity } from 'lucide-react';

const Dashboard = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await adminAPI.getStatistics();
        setStats(response.data);
      } catch (err) {
        setError('فشل في تحميل الإحصائيات');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const statCards = [
    {
      title: 'إجمالي المستخدمين',
      value: stats?.totalUsers || 0,
      icon: Users,
      variant: 'primary'
    },
    {
      title: 'إجمالي الدورات',
      value: stats?.totalCourses || 0,
      icon: BookOpen,
      variant: 'success'
    },
    {
      title: 'نشط اليوم',
      value: stats?.activeToday || 0,
      icon: Activity,
      variant: 'warning'
    },
    {
      title: 'المديرون',
      value: stats?.totalAdmins || 0,
      icon: UserCheck,
      variant: 'info'
    }
  ];

  if (loading) {
    return (
      <div className="text-center py-5">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">جاري التحميل...</span>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2 className="fw-bold">لوحة التحكم</h2>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
        </Alert>
      )}

      <Row className="g-4 mb-5">
        {statCards.map((stat, index) => (
          <Col key={index} sm={6} lg={3}>
            <Card className="border-0 shadow-sm h-100">
              <Card.Body className="text-center">
                <stat.icon size={48} className={`text-${stat.variant} mb-3`} />
                <h3 className="fw-bold mb-1">{stat.value.toLocaleString()}</h3>
                <p className="text-muted mb-0">{stat.title}</p>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      <Row className="g-4">
        <Col lg={6}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Header className="bg-white border-0">
              <h5 className="fw-bold mb-0">إجراءات سريعة</h5>
            </Card.Header>
            <Card.Body>
              <div className="d-flex flex-column gap-2">
                <a href="/admin/add-course" className="btn btn-outline-primary">
                  <BookOpen size={16} className="me-2" />
                  إضافة دورة جديدة
                </a>
                <a href="/admin/users" className="btn btn-outline-success">
                  <Users size={16} className="me-2" />
                  إدارة المستخدمين
                </a>
                <a href="/admin/courses" className="btn btn-outline-info">
                  <Activity size={16} className="me-2" />
                  عرض جميع الدورات
                </a>
              </div>
            </Card.Body>
          </Card>
        </Col>
        
        <Col lg={6}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Header className="bg-white border-0">
              <h5 className="fw-bold mb-0">نظرة عامة على المنصة</h5>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <span className="text-muted">مشاركة المستخدمين</span>
                  <span className="fw-bold">
                    {stats ? Math.round((stats.activeToday / stats.totalUsers) * 100) : 0}%
                  </span>
                </div>
                <div className="progress" style={{ height: '8px' }}>
                  <div 
                    className="progress-bar bg-success" 
                    style={{ width: `${stats ? (stats.activeToday / stats.totalUsers) * 100 : 0}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="mb-3">
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <span className="text-muted">تغطية المديرين</span>
                  <span className="fw-bold">
                    {stats ? Math.round((stats.totalAdmins / stats.totalUsers) * 100) : 0}%
                  </span>
                </div>
                <div className="progress" style={{ height: '8px' }}>
                  <div 
                    className="progress-bar bg-warning" 
                    style={{ width: `${stats ? (stats.totalAdmins / stats.totalUsers) * 100 : 0}%` }}
                  ></div>
                </div>
              </div>

              <small className="text-muted">
                مقاييس صحة المنصة محدثة في الوقت الفعلي
              </small>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
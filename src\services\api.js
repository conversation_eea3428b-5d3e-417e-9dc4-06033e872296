import axios from 'axios';

const API_URL = 'http://localhost:5001/api';

const api = axios.create({
  baseURL: API_URL,
});

// Add token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle token expiration
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const authAPI = {
  register: (userData) => api.post('/auth/register', userData),
  login: (userData) => api.post('/auth/login', userData),
  getProfile: () => api.get('/user/profile'), 
  updateProfile: (id, data) => api.put(`/user/profile/${id}`, data),
};

export const coursesAPI = {
  // الوظائف الأساسية
  getAll: (params) => api.get('/courses', { params }),
  getById: (id) => api.get(`/courses/${id}`),
  create: (formData) => api.post('/courses', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  update: (id, formData) => api.put(`/courses/${id}`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),

  // إدارة حالة الدورة
  toggleStatus: (id) => api.patch(`/courses/${id}/toggle-status`),
  softDelete: (id) => api.patch(`/courses/${id}/soft-delete`),
  permanentDelete: (id) => api.delete(`/courses/${id}`),
  duplicate: (id) => api.post(`/courses/${id}/duplicate`),

  // البحث والإحصائيات
  advancedSearch: (params) => api.get('/courses/search/advanced', { params }),
  getStats: () => api.get('/courses/stats/overview'),

  // إدارة الدروس
  updateLesson: (courseId, unitIndex, lessonIndex, lessonData) =>
    api.put(`/courses/${courseId}/units/${unitIndex}/lessons/${lessonIndex}`, lessonData),
  addLesson: (courseId, unitIndex, lessonData) =>
    api.post(`/courses/${courseId}/units/${unitIndex}/lessons`, lessonData),
  deleteLesson: (courseId, unitIndex, lessonIndex) =>
    api.delete(`/courses/${courseId}/units/${unitIndex}/lessons/${lessonIndex}`),
};
export const userAPI = {
  getUsers: () => api.get('/user/users'),

  getProfile: () => api.get('/user/profile'),
  // updateProfile: (id, data) => api.put(`/user/profile/${id}`, data),
  getCourses: () => api.get('/user/courses'),

}

export const adminAPI = {
  getUsers: () => api.get('/admin/users'),
  // updateUserRole: (id, role) => api.put(`/admin/users/${id}/role`, { role }),
  // deleteUser: (id) => api.delete(`/admin/users/${id}`),
  getCourses: () => api.get('/admin/courses'),
  getStatistics: () => api.get('/admin/statistics'),
};
export const supedAdminAPI = {
  getUsers: () => api.get('/admin/users'),
  updateUserRole: (id, role) => api.put(`/super-admin/users/${id}/role`, { role }),
  deleteUser: (id) => api.delete(`/super-admin/users/${id}`),
  getCourses: () => api.get('/admin/courses'),
  getStatistics: () => api.get('/admin/statistics'),
};

export const chatbotAPI = {
  sendMessage: (message) => api.post('/chatbot', { message }),
};

export default api;